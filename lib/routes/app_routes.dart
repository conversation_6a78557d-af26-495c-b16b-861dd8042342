import 'package:flutter/material.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/signup_screen/signup_screen.dart';
import '../presentation/super_admin_dashboard/super_admin_dashboard.dart';
import '../presentation/restaurant_browse_screen/restaurant_browse_screen.dart';
import '../presentation/restaurant_dashboard/restaurant_dashboard.dart';
import '../presentation/rider_dashboard/rider_dashboard.dart';
import '../presentation/menu_management_screen/menu_management_screen.dart';
import '../presentation/delivery_tracking_screen/delivery_tracking_screen.dart';
import '../presentation/restaurant_menu_screen/restaurant_menu_screen.dart';
import '../presentation/shopping_cart_screen/shopping_cart_screen.dart';
import '../presentation/order_tracking_screen/order_tracking_screen.dart';

class AppRoutes {
  static const String initial = '/';
  static const String home = '/home';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String superAdminDashboard = '/super-admin-dashboard';
  static const String userManagementScreen = '/user-management';
  static const String restaurantManagementScreen = '/restaurant-management';
  static const String restaurantBrowseScreen = '/restaurant-browse-screen';
  static const String restaurantDashboard = '/restaurant-dashboard';
  static const String riderDashboard = '/rider-dashboard';
  static const String menuManagementScreen = '/menu-management-screen';
  static const String deliveryTrackingScreen = '/delivery-tracking-screen';
  static const String restaurantMenuScreen = '/restaurant-menu-screen';
  static const String shoppingCartScreen = '/shopping-cart-screen';
  static const String orderTrackingScreen = '/order-tracking-screen';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const LoginScreen(),
    login: (context) => const LoginScreen(),
    signup: (context) => const SignupScreen(),
    superAdminDashboard: (context) => const SuperAdminDashboard(),
    restaurantBrowseScreen: (context) => const RestaurantBrowseScreen(),
    restaurantDashboard: (context) => const RestaurantDashboard(),
    riderDashboard: (context) => const RiderDashboard(),
    menuManagementScreen: (context) => const MenuManagementScreen(),
    deliveryTrackingScreen: (context) => const DeliveryTrackingScreen(),
    restaurantMenuScreen: (context) =>
        const RestaurantMenuScreen(restaurantId: ''),
    shoppingCartScreen: (context) => const ShoppingCartScreen(),
    orderTrackingScreen: (context) => const OrderTrackingScreen(),
  };
}
