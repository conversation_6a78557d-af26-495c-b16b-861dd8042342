import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/super_admin/providers/restaurant_management_provider.dart';
import 'package:foodflow/models/restaurant.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';

class RestaurantManagementScreen extends ConsumerWidget {
  const RestaurantManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final restaurantsAsync = ref.watch(restaurantManagementProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Restaurant Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showRestaurantDialog(context, ref);
            },
          ),
        ],
      ),
      body: restaurantsAsync.when(
        data: (restaurants) {
          if (restaurants.isEmpty) {
            return const Center(child: Text('No restaurants found.'));
          }
          return ListView.builder(
            itemCount: restaurants.length,
            itemBuilder: (context, index) {
              final restaurant = restaurants[index];
              return ListTile(
                leading: Image.network(restaurant.imageUrl!),
                title: Text(restaurant.name),
                subtitle: Text('${restaurant.address} - ${restaurant.rating}'),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () {
                        _showRestaurantDialog(context, ref,
                            restaurant: restaurant);
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        ref
                            .read(restaurantManagementProvider.notifier)
                            .deleteRestaurant(restaurant.id);
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }

  void _showRestaurantDialog(BuildContext context, WidgetRef ref,
      {Restaurant? restaurant}) {
    final isEditing = restaurant != null;
    final nameController = TextEditingController(text: restaurant?.name);
    final descriptionController =
        TextEditingController(text: restaurant?.description);
    final addressController = TextEditingController(text: restaurant?.address);
    final phoneController = TextEditingController(text: restaurant?.phone);
    final emailController = TextEditingController(text: restaurant?.email);
    final imageUrlController =
        TextEditingController(text: restaurant?.imageUrl);
    final deliveryFeeController =
        TextEditingController(text: restaurant?.deliveryFee.toString());
    final minimumOrderController =
        TextEditingController(text: restaurant?.minimumOrder.toString());
    final deliveryTimeMinutesController =
        TextEditingController(text: restaurant?.deliveryTimeMinutes.toString());
    final cuisineTypeController =
        TextEditingController(text: restaurant?.cuisineType);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Restaurant' : 'Add Restaurant'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Name'),
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(labelText: 'Description'),
              ),
              TextField(
                controller: addressController,
                decoration: const InputDecoration(labelText: 'Address'),
              ),
              TextField(
                controller: phoneController,
                decoration: const InputDecoration(labelText: 'Phone'),
              ),
              TextField(
                controller: emailController,
                decoration: const InputDecoration(labelText: 'Email'),
              ),
              TextField(
                controller: imageUrlController,
                decoration: const InputDecoration(labelText: 'Image URL'),
              ),
              TextField(
                controller: deliveryFeeController,
                decoration: const InputDecoration(labelText: 'Delivery Fee'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: minimumOrderController,
                decoration: const InputDecoration(labelText: 'Minimum Order'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: deliveryTimeMinutesController,
                decoration:
                    const InputDecoration(labelText: 'Delivery Time (minutes)'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: cuisineTypeController,
                decoration: const InputDecoration(labelText: 'Cuisine Type'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final now = DateTime.now();
              final newRestaurant = Restaurant(
                id: restaurant?.id ?? '',
                ownerId: restaurant?.ownerId ??
                    '', // This needs to be handled if adding a new restaurant
                name: nameController.text,
                description: descriptionController.text.isEmpty
                    ? null
                    : descriptionController.text,
                address: addressController.text,
                phone:
                    phoneController.text.isEmpty ? null : phoneController.text,
                email:
                    emailController.text.isEmpty ? null : emailController.text,
                imageUrl: imageUrlController.text.isEmpty
                    ? null
                    : imageUrlController.text,
                status: restaurant?.status ?? 'open',
                rating: restaurant?.rating ?? 0.0,
                deliveryFee: double.parse(deliveryFeeController.text),
                minimumOrder: double.parse(minimumOrderController.text),
                deliveryTimeMinutes:
                    int.parse(deliveryTimeMinutesController.text),
                cuisineType: cuisineTypeController.text.isEmpty
                    ? null
                    : cuisineTypeController.text,
                isFeatured: restaurant?.isFeatured ?? false,
                createdAt: restaurant?.createdAt ?? now,
                updatedAt: now,
              );

              if (isEditing) {
                await ref
                    .read(restaurantManagementProvider.notifier)
                    .updateRestaurant(newRestaurant);
              } else {
                // TODO: Implement add restaurant functionality (requires ownerId)
              }
              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            child: Text(isEditing ? 'Save' : 'Add'),
          ),
        ],
      ),
    );
  }
}
